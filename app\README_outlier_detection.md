# Outlier Detection System for Area Analysis

This document describes the modular outlier detection system implemented for the area analysis pipeline. The system provides multiple scikit-learn based methods to identify anomalous component areas in image analysis.

## Overview

The outlier detection system consists of three main modules:

1. **`outlier_detection.py`** - Core detection algorithms and common interface
2. **`outlier_visualization.py`** - Visualization utilities for results
3. **`area_analysis.py`** - Integrated pipeline with outlier detection

## Features

### Implemented Detection Methods

1. **Robust Covariance (EllipticEnvelope)**
   - Assumes data is Gaussian distributed
   - Fits an ellipse to central data points
   - Good for normally distributed area measurements

2. **One-Class SVM (RBF kernel)**
   - Uses Support Vector Machine with RBF kernel
   - Learns decision boundary around normal data
   - Effective for non-linear patterns

3. **One-Class SVM (SGD)**
   - Linear One-Class SVM with Stochastic Gradient Descent
   - Scales linearly with number of samples
   - Faster for large datasets

4. **Isolation Forest**
   - Ensemble method using random trees
   - Isolates anomalies through recursive partitioning
   - Excellent for high-dimensional data

### Common Interface

All detectors implement a common interface:

```python
from outlier_detection import create_detector

# Create detector
detector = create_detector('isolation_forest', contamination=0.1)

# Fit to data
detector.fit(areas)

# Predict outliers (True = outlier, False = normal)
outliers = detector.predict()

# Get outlier scores
scores = detector.decision_function()
```

### Visualization Features

- **Histogram with outlier highlighting** - Shows distribution with outliers in different colors
- **Decision score plots** - Visualizes outlier scores vs. area values
- **Comparison plots** - Side-by-side comparison of multiple methods
- **Summary tables** - Statistical summary of detection results

## Usage

### Basic Usage

```python
from outlier_detection import create_detector
from outlier_visualization import plot_outlier_histogram

# Your area data
areas = [100, 120, 110, 115, 105, 500, 95, 108, 112, 600]

# Create and fit detector
detector = create_detector('isolation_forest', contamination=0.2)
detector.fit(areas)

# Get results
outliers = detector.predict()
scores = detector.decision_function()

# Visualize
plot_outlier_histogram(areas, outliers, detector.name)
```

### Integrated Pipeline

The main `area_analysis.py` script now includes automatic outlier detection:

```bash
pipenv run python area_analysis.py
```

This will:
1. Load and process images
2. Perform connected components analysis
3. Filter by minimum area (2000 pixels)
4. Run all outlier detection methods
5. Display comparison visualizations
6. Show outlier-filtered images for each method

### Configuration

Key parameters:

- **`contamination`** (0.0-0.5): Expected proportion of outliers in the data
- **`min_area`** (int): Minimum component area threshold
- **Method-specific parameters**: Each detector has additional tuning options

## File Structure

```
app/
├── outlier_detection.py          # Core detection algorithms
├── outlier_visualization.py      # Visualization utilities  
├── area_analysis.py              # Main analysis pipeline
├── test_outlier_detection.py     # Test script
└── README_outlier_detection.md   # This documentation
```

## Dependencies

The system requires these packages (automatically installed via Pipfile):

- `scikit-learn` - Machine learning algorithms
- `matplotlib` - Plotting and visualization
- `numpy` - Numerical computations
- `opencv-python` - Image processing
- `loguru` - Logging

## Testing

Run the test script to verify the system:

```bash
pipenv run python test_outlier_detection.py
```

This creates synthetic data with known outliers and tests all detection methods.

## Performance Notes

- **Robust Covariance**: Fast, works well with Gaussian data
- **One-Class SVM (RBF)**: Moderate speed, good for non-linear patterns
- **One-Class SVM (SGD)**: Fastest, linear complexity
- **Isolation Forest**: Good balance of speed and accuracy

For large datasets (>1000 components), consider using SGD One-Class SVM or Isolation Forest.

## Integration with Existing Pipeline

The outlier detection system is fully integrated with the existing area analysis pipeline:

1. **Color filtering** → Creates binary mask
2. **Connected components** → Identifies individual components  
3. **Area filtering** → Removes components < min_area
4. **Outlier detection** → Identifies anomalous components
5. **Visualization** → Shows results and filtered images

Each step preserves the spatial relationship between components and their areas, allowing for accurate outlier removal from the final images.
