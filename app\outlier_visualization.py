"""
Visualization utilities for outlier detection results.

This module provides functions to visualize outlier detection results
including histograms with outlier highlighting and comparison plots.
"""

import matplotlib.pyplot as plt
import numpy as np
from typing import Dict, List
from loguru import logger

from outlier_detection import OutlierDetector


def plot_outlier_histogram(
    areas: List[int], 
    outliers: np.ndarray, 
    detector_name: str,
    title: str | None = None,
    bins: int = 30,
    figsize: tuple = (12, 8)
) -> None:
    """
    Plot histogram with outliers highlighted.
    
    Args:
        areas: List of component areas
        outliers: Boolean array indicating outliers
        detector_name: Name of the detection method
        title: Custom title for the plot
        bins: Number of histogram bins
        figsize: Figure size tuple
    """
    if not areas:
        logger.warning("No areas to plot")
        return
    
    areas_array = np.array(areas)
    normal_areas = areas_array[~outliers]
    outlier_areas = areas_array[outliers]
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=figsize, height_ratios=[3, 1])
    
    # Main histogram
    ax1.hist(normal_areas, bins=bins, alpha=0.7, color='blue', 
             label=f'Normal ({len(normal_areas)})', edgecolor='black')
    
    if len(outlier_areas) > 0:
        ax1.hist(outlier_areas, bins=bins, alpha=0.8, color='red', 
                 label=f'Outliers ({len(outlier_areas)})', edgecolor='black')
    
    ax1.set_xlabel('Area (pixels)')
    ax1.set_ylabel('Frequency')
    ax1.set_title(title or f'Area Distribution - {detector_name}')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Add statistics
    mean_area = float(np.mean(areas))
    median_area = float(np.median(areas))
    ax1.axvline(mean_area, color='green', linestyle='--', alpha=0.8, 
                label=f'Mean: {mean_area:.1f}')
    ax1.axvline(median_area, color='orange', linestyle='--', alpha=0.8, 
                label=f'Median: {median_area:.1f}')
    
    # Outlier scatter plot
    y_positions = np.random.uniform(0, 1, len(areas))
    colors = ['red' if is_outlier else 'blue' for is_outlier in outliers]
    ax2.scatter(areas, y_positions, c=colors, alpha=0.6, s=20)
    ax2.set_xlabel('Area (pixels)')
    ax2.set_ylabel('Random Y')
    ax2.set_title('Outlier Detection Results (Red = Outlier)')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Log statistics
    outlier_count = np.sum(outliers)
    outlier_percentage = (outlier_count / len(areas)) * 100
    logger.info(f"{detector_name}: {outlier_count}/{len(areas)} outliers ({outlier_percentage:.1f}%)")
    
    if len(outlier_areas) > 0:
        logger.info(f"Outlier area range: {np.min(outlier_areas):.1f} - {np.max(outlier_areas):.1f}")
    
    plt.show()


def plot_decision_scores(
    areas: List[int],
    scores: np.ndarray,
    outliers: np.ndarray,
    detector_name: str,
    figsize: tuple = (10, 6)
) -> None:
    """
    Plot decision function scores.
    
    Args:
        areas: List of component areas
        scores: Decision function scores
        outliers: Boolean array indicating outliers
        detector_name: Name of the detection method
        figsize: Figure size tuple
    """
    plt.figure(figsize=figsize)
    
    # Scatter plot of areas vs scores
    colors = ['red' if is_outlier else 'blue' for is_outlier in outliers]
    plt.scatter(areas, scores, c=colors, alpha=0.6)
    
    # Add threshold line (typically at 0 for most detectors)
    plt.axhline(y=0, color='black', linestyle='--', alpha=0.5, 
                label='Decision Threshold')
    
    plt.xlabel('Area (pixels)')
    plt.ylabel('Decision Score')
    plt.title(f'Decision Scores - {detector_name}')
    plt.legend(['Threshold', 'Normal', 'Outlier'])
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()


def compare_detectors(
    areas: List[int],
    detectors: Dict[str, OutlierDetector],
    figsize: tuple = (15, 10)
) -> None:
    """
    Compare multiple outlier detectors side by side.
    
    Args:
        areas: List of component areas
        detectors: Dictionary of fitted detectors
        figsize: Figure size tuple
    """
    n_detectors = len(detectors)
    if n_detectors == 0:
        logger.warning("No detectors provided for comparison")
        return
    
    # Calculate grid dimensions
    cols = min(2, n_detectors)
    rows = (n_detectors + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=figsize)
    if n_detectors == 1:
        axes = [axes]
    elif rows == 1:
        axes = [axes]
    else:
        axes = axes.flatten()
    
    areas_array = np.array(areas)
    
    for idx, (name, detector) in enumerate(detectors.items()):
        ax = axes[idx]
        
        # Get predictions
        outliers = detector.predict()
        normal_areas = areas_array[~outliers]
        outlier_areas = areas_array[outliers]
        
        # Plot histogram
        ax.hist(normal_areas, bins=20, alpha=0.7, color='blue', 
                label=f'Normal ({len(normal_areas)})', edgecolor='black')
        
        if len(outlier_areas) > 0:
            ax.hist(outlier_areas, bins=20, alpha=0.8, color='red', 
                    label=f'Outliers ({len(outlier_areas)})', edgecolor='black')
        
        ax.set_xlabel('Area (pixels)')
        ax.set_ylabel('Frequency')
        ax.set_title(f'{detector.name}')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Add outlier percentage to title
        outlier_percentage = (len(outlier_areas) / len(areas)) * 100
        ax.text(0.02, 0.98, f'{outlier_percentage:.1f}% outliers', 
                transform=ax.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # Hide unused subplots
    for idx in range(n_detectors, len(axes)):
        axes[idx].set_visible(False)
    
    plt.tight_layout()
    plt.show()


def plot_outlier_summary_table(
    areas: List[int],
    detectors: Dict[str, OutlierDetector]
) -> None:
    """
    Create a summary table of outlier detection results.
    
    Args:
        areas: List of component areas
        detectors: Dictionary of fitted detectors
    """
    if not detectors:
        logger.warning("No detectors provided for summary")
        return
    
    # Collect statistics
    results = []
    for name, detector in detectors.items():
        outliers = detector.predict()
        outlier_count = np.sum(outliers)
        outlier_percentage = (outlier_count / len(areas)) * 100
        
        if outlier_count > 0:
            outlier_areas = np.array(areas)[outliers]
            min_outlier = np.min(outlier_areas)
            max_outlier = np.max(outlier_areas)
            mean_outlier = np.mean(outlier_areas)
        else:
            min_outlier = max_outlier = mean_outlier = 0
        
        results.append({
            'Method': detector.name,
            'Outliers': outlier_count,
            'Percentage': f'{outlier_percentage:.1f}%',
            'Min Area': f'{min_outlier:.0f}' if outlier_count > 0 else 'N/A',
            'Max Area': f'{max_outlier:.0f}' if outlier_count > 0 else 'N/A',
            'Mean Area': f'{mean_outlier:.1f}' if outlier_count > 0 else 'N/A'
        })
    
    # Create table plot
    fig, ax = plt.subplots(figsize=(12, len(results) * 0.5 + 2))
    ax.axis('tight')
    ax.axis('off')
    
    # Create table
    table_data = []
    headers = ['Method', 'Outliers', 'Percentage', 'Min Area', 'Max Area', 'Mean Area']
    table_data.append(headers)
    
    for result in results:
        table_data.append([result[header] for header in headers])
    
    table = ax.table(cellText=table_data[1:], colLabels=table_data[0],
                     cellLoc='center', loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 1.5)
    
    # Style the table
    for i in range(len(headers)):
        table[(0, i)].set_facecolor('#40466e')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    plt.title('Outlier Detection Summary', fontsize=14, fontweight='bold', pad=20)
    plt.tight_layout()
    plt.show()
    
    # Also log the results
    logger.info("Outlier Detection Summary:")
    for result in results:
        logger.info(f"  {result['Method']}: {result['Outliers']} outliers ({result['Percentage']})")
