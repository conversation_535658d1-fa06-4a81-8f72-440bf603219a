#!/usr/bin/env python3
"""
Test script for the outlier detection system.
"""

import numpy as np
from loguru import logger
from outlier_detection import create_detector
from outlier_visualization import plot_outlier_histogram, compare_detectors


def test_outlier_detection():
    """Test the outlier detection system with synthetic data."""
    
    # Create test data with clear outliers
    np.random.seed(42)
    normal_areas = np.random.normal(100, 10, 50).astype(int)  # Normal distribution around 100
    outlier_areas = [300, 350, 400, 20, 15]  # Clear outliers
    areas = np.concatenate([normal_areas, outlier_areas]).tolist()
    
    logger.info(f"Testing with {len(areas)} components: {len(normal_areas)} normal + {len(outlier_areas)} outliers")
    logger.info(f"Normal area range: {min(normal_areas)}-{max(normal_areas)}")
    logger.info(f"Outlier areas: {outlier_areas}")
    
    # Test each detector
    methods = ['robust_covariance', 'one_class_svm', 'sgd_one_class_svm', 'isolation_forest']
    detectors = {}
    
    for method in methods:
        try:
            detector = create_detector(method, contamination=0.1)
            detector.fit(areas)
            outliers = detector.predict()
            outlier_indices = np.where(outliers)[0]
            detected_outliers = [areas[i] for i in outlier_indices]
            
            logger.info(f"{detector.name}: Found {len(detected_outliers)} outliers: {detected_outliers}")
            detectors[method] = detector
            
        except Exception as e:
            logger.error(f"{method} failed: {e}")
    
    # Visualize results if we have successful detectors
    if detectors:
        logger.info("Creating comparison visualization...")
        compare_detectors(areas, detectors)
        
        # Show individual results
        for name, detector in detectors.items():
            outliers = detector.predict()
            plot_outlier_histogram(areas, outliers, detector.name)
    
    return detectors


if __name__ == "__main__":
    test_outlier_detection()
