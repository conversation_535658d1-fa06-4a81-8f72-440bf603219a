"""
Modular outlier detection system for area analysis.

This module provides a common interface for different outlier detection methods
and implements various scikit-learn based detectors for identifying anomalous
component areas in image analysis.
"""

from abc import ABC, abstractmethod
from typing import Protocol, runtime_checkable
import numpy as np
from sklearn.covariance import EllipticEnvelope
from sklearn.svm import OneClassSVM
from sklearn.linear_model import SGDOneClassSVM
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
from loguru import logger


@runtime_checkable
class OutlierDetector(Protocol):
    """Protocol defining the interface for outlier detectors."""

    def fit(self, areas: list[int]) -> "OutlierDetector":
        """Fit the outlier detector to the data."""
        ...

    def predict(self, areas: list[int] | None = None) -> np.ndarray:
        """Predict outliers. Returns boolean array (True = outlier, False = normal)."""
        ...

    def decision_function(self, areas: list[int] | None = None) -> np.ndarray:
        """Return outlier scores/distances."""
        ...

    @property
    def name(self) -> str:
        """Return the name of the detector."""
        ...


class BaseOutlierDetector(ABC):
    """Abstract base class for outlier detectors."""

    def __init__(self, contamination: float = 0.1):
        """
        Initialize the detector.

        Args:
            contamination: Expected proportion of outliers in the data
        """
        self.contamination = contamination
        self._fitted = False
        self._scaler = StandardScaler()
        self._fitted_areas = None

    @abstractmethod
    def _create_detector(self):
        """Create the underlying scikit-learn detector."""
        pass

    @property
    @abstractmethod
    def name(self) -> str:
        """Return the name of the detector."""
        pass

    def fit(self, areas: list[int]) -> "BaseOutlierDetector":
        """
        Fit the outlier detector to the data.

        Args:
            areas: List of component areas

        Returns:
            Self for method chaining
        """
        if not areas:
            raise ValueError("Cannot fit detector with empty areas list")

        # Convert to numpy array and reshape for sklearn
        areas_array = np.array(areas).reshape(-1, 1)

        # Store fitted areas for later use
        self._fitted_areas = areas_array.copy()

        # Standardize the data
        areas_scaled = self._scaler.fit_transform(areas_array)

        # Create and fit the detector
        self._detector = self._create_detector()
        self._detector.fit(areas_scaled)

        self._fitted = True
        logger.info(f"Fitted {self.name} detector with {len(areas)} samples")

        return self

    def predict(self, areas: list[int] | None = None) -> np.ndarray:
        """
        Predict outliers.

        Args:
            areas: List of areas to predict. If None, uses fitted data.

        Returns:
            Boolean array where True indicates outlier
        """
        if not self._fitted:
            raise ValueError("Detector must be fitted before prediction")

        if areas is None:
            areas_array = self._fitted_areas
        else:
            areas_array = np.array(areas).reshape(-1, 1)

        # Standardize using fitted scaler
        areas_scaled = self._scaler.transform(areas_array)

        # Predict (-1 for outlier, 1 for inlier in sklearn)
        predictions = self._detector.predict(areas_scaled)

        # Convert to boolean (True for outlier)
        return predictions == -1

    def decision_function(self, areas: list[int] | None = None) -> np.ndarray:
        """
        Return outlier scores/distances.

        Args:
            areas: List of areas to score. If None, uses fitted data.

        Returns:
            Array of outlier scores (more negative = more outlying)
        """
        if not self._fitted:
            raise ValueError("Detector must be fitted before scoring")

        if areas is None:
            areas_array = self._fitted_areas
        else:
            areas_array = np.array(areas).reshape(-1, 1)

        # Standardize using fitted scaler
        areas_scaled = self._scaler.transform(areas_array)

        return self._detector.decision_function(areas_scaled)


class RobustCovarianceDetector(BaseOutlierDetector):
    """Outlier detector using Robust Covariance (EllipticEnvelope)."""

    def __init__(self, contamination: float = 0.1, support_fraction: float | None = None):
        """
        Initialize the robust covariance detector.

        Args:
            contamination: Expected proportion of outliers
            support_fraction: Proportion of points to include in support
        """
        super().__init__(contamination)
        self.support_fraction = support_fraction

    def _create_detector(self):
        return EllipticEnvelope(
            contamination=self.contamination, support_fraction=self.support_fraction
        )

    @property
    def name(self) -> str:
        return "Robust Covariance (EllipticEnvelope)"


class OneClassSVMDetector(BaseOutlierDetector):
    """Outlier detector using One-Class SVM with RBF kernel."""

    def __init__(self, contamination: float = 0.1, nu: float | None = None, gamma: str = "scale"):
        """
        Initialize the One-Class SVM detector.

        Args:
            contamination: Expected proportion of outliers
            nu: Upper bound on fraction of training errors and lower bound of support vectors
            gamma: Kernel coefficient for RBF kernel
        """
        super().__init__(contamination)
        # Convert contamination to nu if not provided
        self.nu = nu if nu is not None else contamination
        self.gamma = gamma

    def _create_detector(self):
        return OneClassSVM(nu=self.nu, gamma=self.gamma, kernel="rbf")

    @property
    def name(self) -> str:
        return "One-Class SVM (RBF)"


class SGDOneClassSVMDetector(BaseOutlierDetector):
    """Outlier detector using One-Class SVM with SGD."""

    def __init__(
        self, contamination: float = 0.1, nu: float | None = None, learning_rate: str = "constant"
    ):
        """
        Initialize the SGD One-Class SVM detector.

        Args:
            contamination: Expected proportion of outliers
            nu: Upper bound on fraction of training errors
            learning_rate: Learning rate schedule
        """
        super().__init__(contamination)
        self.nu = nu if nu is not None else contamination
        self.learning_rate = learning_rate

    def _create_detector(self):
        return SGDOneClassSVM(
            nu=self.nu,
            learning_rate=self.learning_rate,
            eta0=0.01,  # Initial learning rate
            random_state=42,
        )

    @property
    def name(self) -> str:
        return "One-Class SVM (SGD)"


class IsolationForestDetector(BaseOutlierDetector):
    """Outlier detector using Isolation Forest."""

    def __init__(
        self, contamination: float = 0.1, n_estimators: int = 100, max_samples: str | int = "auto"
    ):
        """
        Initialize the Isolation Forest detector.

        Args:
            contamination: Expected proportion of outliers
            n_estimators: Number of base estimators
            max_samples: Number of samples to draw to train each base estimator
        """
        super().__init__(contamination)
        self.n_estimators = n_estimators
        self.max_samples = max_samples

    def _create_detector(self):
        return IsolationForest(
            contamination=self.contamination,
            n_estimators=self.n_estimators,
            max_samples=self.max_samples,
            random_state=42,
        )

    @property
    def name(self) -> str:
        return "Isolation Forest"


# Factory function for easy detector creation
def create_detector(method: str, **kwargs) -> BaseOutlierDetector:
    """
    Factory function to create outlier detectors.

    Args:
        method: Name of the detection method
        **kwargs: Additional parameters for the detector

    Returns:
        Configured outlier detector

    Raises:
        ValueError: If method is not recognized
    """
    detectors = {
        "robust_covariance": RobustCovarianceDetector,
        "one_class_svm": OneClassSVMDetector,
        "sgd_one_class_svm": SGDOneClassSVMDetector,
        "isolation_forest": IsolationForestDetector,
    }

    if method not in detectors:
        available = ", ".join(detectors.keys())
        raise ValueError(f"Unknown method '{method}'. Available methods: {available}")

    return detectors[method](**kwargs)
