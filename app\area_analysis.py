import pathlib

import cv2 as cv
import numpy as np
import matplotlib.pyplot as plt
from loguru import logger

from color_filter import ColorFilter, apply_color_filter
from image_colorspace import Image, Mask, create_image


def load_directory_as_image(path: pathlib.Path) -> Image:
    """Load all images in a directory as a single image."""
    images = []
    for file in path.iterdir():
        if file.is_file():
            logger.debug(f"Loading {file}")
            images.append(cv.imread(str(file), cv.IMREAD_UNCHANGED))
    image_bgr = np.concatenate(images, axis=0)
    return create_image(image_bgr)


def make_mask(image: Image) -> Mask:
    """Make a mask from the image."""
    filter = ColorFilter(
        color_space="LAB",
        passthrough=[False, False, False],
        lower=[236, 125, 125],
        upper=[255, 140, 140],
        invert=True,
    )
    return apply_color_filter(image, filter)


def analyze_connected_components(mask: Mask, min_area: int = 2000) -> tuple[np.ndarray, list[int]]:
    """
    Analyze connected components in the mask and filter by minimum area.

    Args:
        mask: Binary mask to analyze
        min_area: Minimum area threshold for components

    Returns:
        tuple: (filtered_mask, list_of_areas)
            - filtered_mask: Mask with only components >= min_area
            - list_of_areas: List of areas for all components >= min_area
    """
    # Convert boolean mask to uint8 for OpenCV
    mask_uint8 = mask.astype(np.uint8) * 255

    # Find connected components
    _, labels, stats, _ = cv.connectedComponentsWithStats(mask_uint8, connectivity=8)

    # Extract areas (stats[:, cv.CC_STAT_AREA] gives area for each component)
    # Skip the first component (index 0) as it's the background
    areas = stats[1:, cv.CC_STAT_AREA]

    # Filter components by minimum area
    valid_components = areas >= min_area
    valid_areas = areas[valid_components].tolist()

    # Create filtered mask with only valid components
    filtered_mask = np.zeros_like(mask, dtype=np.bool_)
    for i, is_valid in enumerate(valid_components, start=1):  # start=1 to skip background
        if is_valid:
            filtered_mask |= labels == i

    logger.info(f"Found {len(areas)} total components, {len(valid_areas)} with area >= {min_area}")

    return filtered_mask, valid_areas


def plot_area_histogram(areas: list[int], title: str = "Component Areas Distribution"):
    """
    Plot histogram of component areas.

    Args:
        areas: List of component areas
        title: Title for the histogram
    """
    if not areas:
        logger.warning("No areas to plot")
        return

    plt.figure(figsize=(10, 6))
    plt.hist(areas, bins=30, alpha=0.7, edgecolor="black")
    plt.xlabel("Area (pixels)")
    plt.ylabel("Frequency")
    plt.title(title)
    plt.grid(True, alpha=0.3)

    # Add statistics to the plot
    mean_area = float(np.mean(areas))
    median_area = float(np.median(areas))
    plt.axvline(mean_area, color="red", linestyle="--", label=f"Mean: {mean_area:.1f}")
    plt.axvline(median_area, color="green", linestyle="--", label=f"Median: {median_area:.1f}")
    plt.legend()

    logger.info(
        f"Area statistics: Mean={mean_area:.1f}, Median={median_area:.1f}, "
        f"Min={min(areas)}, Max={max(areas)}, Count={len(areas)}"
    )

    plt.show()


def main(path: pathlib.Path):
    image = load_directory_as_image(path)
    cv.imshow("image", image.BGR[::8, ::8])
    logger.debug(
        f"Image statistics: {np.min(image.BGR, axis=(0, 1))}, {np.max(image.BGR, axis=(0, 1))}"
    )
    cv.waitKey(1)

    mask = make_mask(image)

    # Print some statistics (min, max)
    logger.debug(
        f"Image statistics: {np.min(image.LAB, axis=(0, 1))}, {np.max(image.LAB, axis=(0, 1))}"
    )

    # show original masked image
    masked = cv.bitwise_and(image.BGR, image.BGR, mask=mask.astype(np.uint8))
    cv.imshow("masked", masked[::8, ::8])
    cv.waitKey(1)

    # Perform connected components analysis and filter by area
    filtered_mask, areas = analyze_connected_components(mask)

    # Show filtered mask (only components >= 100 pixels)
    filtered_masked = cv.bitwise_and(image.BGR, image.BGR, mask=filtered_mask.astype(np.uint8))
    cv.imshow("filtered_masked", filtered_masked[::8, ::8])
    cv.waitKey(1)

    # Plot histogram of areas
    if areas:
        plot_area_histogram(areas, "Distribution of Component Areas (≥100 pixels)")
    else:
        logger.warning("No components found with area >= 100 pixels")

    cv.waitKey(0)
    cv.destroyAllWindows()


if __name__ == "__main__":
    main(pathlib.Path("../data/oranje_bolletjes/2025-07-29_20.03.28/"))
