import pathlib

import cv2 as cv
import numpy as np
from loguru import logger

from color_filter import ColorFilter, apply_color_filter
from image_colorspace import Image, Mask, create_image


def load_directory_as_image(path: pathlib.Path) -> Image:
    """Load all images in a directory as a single image."""
    images = []
    for file in path.iterdir():
        if file.is_file():
            logger.debug(f"Loading {file}")
            images.append(cv.imread(str(file), cv.IMREAD_UNCHANGED))
    image_bgr = np.concatenate(images, axis=0)
    return create_image(image_bgr)


def make_mask(image: Image) -> Mask:
    """Make a mask from the image."""
    filter = ColorFilter(
        color_space="LAB",
        passthrough=[False, False, False],
        lower=[236, 125, 125],
        upper=[255, 140, 140],
        invert=True,
    )
    return apply_color_filter(image, filter)


def main(path: pathlib.Path):
    image = load_directory_as_image(path)
    cv.imshow("image", image.BGR[::8, ::8])
    logger.debug(
        f"Image statistics: {np.min(image.BGR, axis=(0, 1))}, {np.max(image.BGR, axis=(0, 1))}"
    )
    cv.waitKey(1)

    mask = make_mask(image)

    # Print some statistics (min, max)
    logger.debug(
        f"Image statistics: {np.min(image.LAB, axis=(0, 1))}, {np.max(image.LAB, axis=(0, 1))}"
    )

    # show masked
    masked = cv.bitwise_and(image.BGR, image.BGR, mask=mask.astype(np.uint8))
    cv.imshow("masked", masked[::8, ::8])

    cv.waitKey(0)


if __name__ == "__main__":
    main(pathlib.Path("../data/oranje_bolletjes/2025-07-29_20.03.28/"))
